2025-08-12 13:21:26.015 [Test worker] INFO  o.g.a.i.t.testing.worker.TestWorker - Gradle Test Executor 1 started executing tests.
2025-08-12 13:21:26.264 [Test worker] INFO  c.logictrue.service.DatabaseService - 数据库服务基础初始化完成，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:21:26.270 [Test worker] INFO  c.l.s.DatabaseServiceCircularDependencyTest - DatabaseService初始化测试通过，数据库路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:21:26.283 [Test worker] INFO  c.l.s.DatabaseServiceCircularDependencyTest - 数据库路径获取测试通过: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:21:26.288 [Test worker] INFO  c.l.s.DatabaseServiceCircularDependencyTest - 多次调用稳定性测试通过
2025-08-12 13:21:26.293 [Test worker] INFO  c.l.s.DatabaseServiceCircularDependencyTest - 单例模式测试通过
2025-08-12 13:21:26.301 [Test worker] INFO  c.logictrue.config.MyBatisPlusConfig - 配置SQLite数据源，路径: /home/<USER>/nl-mes/iot-nl/iot-jfx/data_iot.db
2025-08-12 13:21:26.320 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 13:21:26.518 [Test worker] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@3956b302
2025-08-12 13:21:26.520 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 13:21:26.904 [Test worker] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionBasicFieldMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionBasicFieldMapper.xml
	at org.mybatis@3.5.6/org.apache.ibatis.io.Resources.getResourceAsStream(Resources.java:114)
	at org.mybatis@3.5.6/org.apache.ibatis.io.Resources.getResourceAsStream(Resources.java:100)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(MyBatisPlusConfig.java:171)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(MyBatisPlusConfig.java:141)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(MyBatisPlusConfig.java:57)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(MyBatisPlusConfig.java:35)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(MyBatisPlusConfig.java:43)
	at com.logictrue@1.0/com.logictrue.service.DatabaseServiceCircularDependencyTest.testMyBatisPlusConfigInitialization(DatabaseServiceCircularDependencyTest.java:46)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons@1.10.0/org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy3/jdk.proxy3.$Proxy6.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-08-12 13:21:26.913 [Test worker] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionTableHeaderMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionTableHeaderMapper.xml
	at org.mybatis@3.5.6/org.apache.ibatis.io.Resources.getResourceAsStream(Resources.java:114)
	at org.mybatis@3.5.6/org.apache.ibatis.io.Resources.getResourceAsStream(Resources.java:100)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(MyBatisPlusConfig.java:171)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(MyBatisPlusConfig.java:141)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(MyBatisPlusConfig.java:57)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(MyBatisPlusConfig.java:35)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(MyBatisPlusConfig.java:43)
	at com.logictrue@1.0/com.logictrue.service.DatabaseServiceCircularDependencyTest.testMyBatisPlusConfigInitialization(DatabaseServiceCircularDependencyTest.java:46)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons@1.10.0/org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy3/jdk.proxy3.$Proxy6.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-08-12 13:21:26.917 [Test worker] ERROR c.logictrue.config.MyBatisPlusConfig - 加载XML映射文件失败: mapper/DeviceDetectionTableDataMapper.xml
java.io.IOException: Could not find resource mapper/DeviceDetectionTableDataMapper.xml
	at org.mybatis@3.5.6/org.apache.ibatis.io.Resources.getResourceAsStream(Resources.java:114)
	at org.mybatis@3.5.6/org.apache.ibatis.io.Resources.getResourceAsStream(Resources.java:100)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.loadXmlMappers(MyBatisPlusConfig.java:171)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.createSqlSessionFactory(MyBatisPlusConfig.java:141)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.initializeMyBatisPlus(MyBatisPlusConfig.java:57)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.<init>(MyBatisPlusConfig.java:35)
	at com.logictrue@1.0/com.logictrue.config.MyBatisPlusConfig.getInstance(MyBatisPlusConfig.java:43)
	at com.logictrue@1.0/com.logictrue.service.DatabaseServiceCircularDependencyTest.testMyBatisPlusConfigInitialization(DatabaseServiceCircularDependencyTest.java:46)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons@1.10.0/org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine@5.10.0/org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine@1.10.0/org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy3/jdk.proxy3.$Proxy6.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-08-12 13:21:26.937 [Test worker] INFO  c.logictrue.config.MyBatisPlusConfig - MyBatis-Plus初始化成功
2025-08-12 13:21:26.938 [Test worker] INFO  c.l.s.DatabaseServiceCircularDependencyTest - MyBatisPlusConfig独立初始化测试通过
2025-08-12 13:21:26.957 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:26.959 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.service.DynamicTableCreationServiceTest$TestEntity
2025-08-12 13:21:26.961 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=test_entity, 字段数=3
2025-08-12 13:21:26.962 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 实体类注解解析测试通过，表名: test_entity, 列数: 3
2025-08-12 13:21:26.963 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:26.964 [Test worker] INFO  c.l.s.DynamicTableCreationService - 开始动态创建数据库表结构
2025-08-12 13:21:26.969 [Test worker] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 13:21:26.969 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionData
2025-08-12 13:21:26.970 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_data, 字段数=20
2025-08-12 13:21:26.976 [Test worker] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_data
2025-08-12 13:21:26.977 [Test worker] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_data (id INTEGER PRIMARY KEY AUTOINCREMENT, device_code TEXT, template_id INTEGER, template_code TEXT, template_name TEXT, file_name TEXT, file_path TEXT, file_size INTEGER, parse_status INTEGER, parse_message TEXT, parse_time TIMESTAMP, total_sheets INTEGER, parsed_sheets INTEGER, basic_fields_count INTEGER, table_rows_count INTEGER, create_by TEXT, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, update_by TEXT, update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, remark TEXT)
2025-08-12 13:21:26.983 [Test worker] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_data 创建成功
2025-08-12 13:21:26.985 [Test worker] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_data 创建了 9 个索引
2025-08-12 13:21:26.985 [Test worker] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 13:21:26.985 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionBasicField
2025-08-12 13:21:26.985 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_basic_field, 字段数=17
2025-08-12 13:21:26.986 [Test worker] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_basic_field
2025-08-12 13:21:26.986 [Test worker] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_basic_field (id INTEGER PRIMARY KEY AUTOINCREMENT, detection_data_id INTEGER, sheet_id TEXT, sheet_name TEXT, sheet_index INTEGER, field_code TEXT, field_name TEXT, field_type TEXT, field_value TEXT, label_position TEXT, value_position TEXT, label_row_index INTEGER, label_col_index INTEGER, value_row_index INTEGER, value_col_index INTEGER, sort_order INTEGER, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
2025-08-12 13:21:26.986 [Test worker] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_basic_field 创建成功
2025-08-12 13:21:26.988 [Test worker] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_basic_field 创建了 7 个索引
2025-08-12 13:21:26.988 [Test worker] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 13:21:26.988 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableHeader
2025-08-12 13:21:26.988 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_header, 字段数=13
2025-08-12 13:21:26.989 [Test worker] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_table_header
2025-08-12 13:21:26.989 [Test worker] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_table_header (id INTEGER PRIMARY KEY AUTOINCREMENT, detection_data_id INTEGER, sheet_id TEXT, sheet_name TEXT, sheet_index INTEGER, header_name TEXT, header_code TEXT, header_position TEXT, header_row_index INTEGER, header_col_index INTEGER, data_type TEXT, column_order INTEGER, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
2025-08-12 13:21:26.992 [Test worker] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_header 创建成功
2025-08-12 13:21:26.994 [Test worker] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_table_header 创建了 7 个索引
2025-08-12 13:21:26.994 [Test worker] INFO  c.l.s.DynamicTableCreationService - 开始处理实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 13:21:26.995 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.iot.entity.DeviceDetectionTableData
2025-08-12 13:21:26.995 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=device_detection_table_data, 字段数=9
2025-08-12 13:21:26.995 [Test worker] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: device_detection_table_data
2025-08-12 13:21:26.995 [Test worker] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS device_detection_table_data (id INTEGER PRIMARY KEY AUTOINCREMENT, detection_data_id INTEGER, sheet_id TEXT, sheet_name TEXT, sheet_index INTEGER, row_index INTEGER, row_data TEXT, row_order INTEGER, create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
2025-08-12 13:21:26.996 [Test worker] INFO  c.l.s.DynamicTableCreationService - 表 device_detection_table_data 创建成功
2025-08-12 13:21:26.997 [Test worker] INFO  c.l.s.DynamicTableCreationService - 为表 device_detection_table_data 创建了 4 个索引
2025-08-12 13:21:26.997 [Test worker] INFO  c.l.s.DynamicTableCreationService - 动态表创建完成，共处理 4 个实体类
2025-08-12 13:21:27.015 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 获取表结构信息测试通过，列数: 20
2025-08-12 13:21:27.020 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:27.020 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 单例模式测试通过
2025-08-12 13:21:27.025 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:27.025 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.service.DynamicTableCreationServiceTest$TestEntity
2025-08-12 13:21:27.025 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=test_entity, 字段数=3
2025-08-12 13:21:27.026 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 表比较测试通过，表名: test_entity, 需要更新: true
2025-08-12 13:21:27.029 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:27.031 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 工具方法测试完成
2025-08-12 13:21:27.034 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:27.035 [Test worker] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: test_table
2025-08-12 13:21:27.035 [Test worker] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT NOT NULL)
2025-08-12 13:21:27.035 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - SQL生成测试通过
2025-08-12 13:21:27.035 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 索引SQL生成测试通过，索引数: 1
2025-08-12 13:21:27.042 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - 测试环境初始化完成
2025-08-12 13:21:27.043 [Test worker] INFO  c.l.s.DynamicTableCreationServiceTest - MyBatis Plus配置获取测试通过
2025-08-12 13:21:27.046 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.service.DynamicTableCreationSimpleTest$TestEntity
2025-08-12 13:21:27.047 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=test_entity, 字段数=3
2025-08-12 13:21:27.047 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 实体注解解析测试通过
2025-08-12 13:21:27.051 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 驼峰命名转换测试通过
2025-08-12 13:21:27.056 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.service.DynamicTableCreationSimpleTest$TestEntity
2025-08-12 13:21:27.058 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=test_entity, 字段数=3
2025-08-12 13:21:27.059 [Test worker] INFO  c.l.service.DynamicSqlGenerator - 开始生成CREATE TABLE语句，表名: test_entity
2025-08-12 13:21:27.059 [Test worker] INFO  c.l.service.DynamicSqlGenerator - CREATE TABLE语句生成完成: CREATE TABLE IF NOT EXISTS test_entity (id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, status INTEGER)
2025-08-12 13:21:27.059 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 生成的SQL: CREATE TABLE IF NOT EXISTS test_entity (id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, status INTEGER)
2025-08-12 13:21:27.060 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 生成了 2 个索引
2025-08-12 13:21:27.060 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 索引SQL: CREATE INDEX IF NOT EXISTS idx_test_entity_name ON test_entity (name)
2025-08-12 13:21:27.060 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 索引SQL: CREATE INDEX IF NOT EXISTS idx_test_entity_status ON test_entity (status)
2025-08-12 13:21:27.060 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - SQL生成测试通过
2025-08-12 13:21:27.062 [Test worker] INFO  c.l.service.EntityAnnotationParser - 开始解析实体类: com.logictrue.service.DynamicTableCreationSimpleTest$ComplexTestEntity
2025-08-12 13:21:27.063 [Test worker] INFO  c.l.service.EntityAnnotationParser - 实体类解析完成: 表名=complex_test_entity, 字段数=5
2025-08-12 13:21:27.063 [Test worker] INFO  c.l.s.DynamicTableCreationSimpleTest - 数据类型映射测试通过
2025-08-12 13:21:27.067 [Test worker] INFO  o.g.a.i.t.testing.worker.TestWorker - Gradle Test Executor 1 finished executing tests.
